'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PreloaderProps {
  onComplete: () => void;
}

export default function PreloaderWithText({ onComplete }: PreloaderProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [displayText, setDisplayText] = useState('');

  const fullText = "Hi darling, welcome to Urvashi...";

  // Simple typing effect - 2 seconds total
  useEffect(() => {
    if (currentStep === 0) {
      let index = 0;
      const timer = setInterval(() => {
        if (index <= fullText.length) {
          setDisplayText(fullText.slice(0, index));
          index++;
        } else {
          clearInterval(timer);
          setTimeout(() => setCurrentStep(1), 200); // Brief pause
        }
      }, 60); // 2 seconds total for typing
      return () => clearInterval(timer);
    }
  }, [currentStep]);

  // Logo animation - 1 second
  useEffect(() => {
    if (currentStep === 1) {
      setTimeout(() => setCurrentStep(2), 1000); // 1 second logo
    }
  }, [currentStep]);

  // Exit
  useEffect(() => {
    if (currentStep === 2) {
      setIsVisible(false);
      setTimeout(onComplete, 300);
    }
  }, [currentStep, onComplete]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-black"
        >
          {/* Main content container */}
          <div className="relative z-10 text-center max-w-lg mx-auto px-6">
            {/* Simple Typing Phase */}
            <AnimatePresence>
              {currentStep === 0 && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <p className="text-2xl md:text-3xl text-white font-light">
                    {displayText}
                    <motion.span
                      animate={{ opacity: [1, 0] }}
                      transition={{ duration: 0.8, repeat: Infinity }}
                      className="text-[#F66581] ml-1"
                    >
                      |
                    </motion.span>
                  </p>
                </motion.div>
              )}
            </AnimatePresence>

            {/* Simple Logo Animation */}
            <AnimatePresence>
              {currentStep === 1 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 1, ease: "easeOut" }}
                  className="flex items-center justify-center"
                >
                  <div className="w-40 h-20 md:w-64 md:h-24 relative">
                    <Image
                      src="/logo.png"
                      alt="Urvashi Logo"
                      fill
                      className="object-contain drop-shadow-lg"
                      priority
                      unoptimized
                    />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
