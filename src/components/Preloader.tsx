'use client';

import Image from 'next/image';
import { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

interface PreloaderProps {
  onComplete: () => void;
}

export default function Preloader({ onComplete }: PreloaderProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  // Logo animation - 1 second
  useEffect(() => {
    if (currentStep === 0) {
      setTimeout(() => setCurrentStep(1), 1300);
    }
  }, [currentStep]);

  // Exit
  useEffect(() => {
    if (currentStep === 1) {
      setIsVisible(false);
      setTimeout(onComplete, 200);
    }
  }, [currentStep, onComplete]);

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-gradient-to-br from-black via-gray-900 to-black"
        >
          {/* Main content container */}
          <div className="relative z-10 text-center max-w-lg mx-auto px-6">
            {/* Simple Logo Animation */}
            <AnimatePresence>
              {currentStep === 0 && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.7 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 1.2, ease: "easeInOut" }}
                  className="flex items-center justify-center"
                >
                  <div className="w-40 h-20 md:w-64 md:h-24 relative">
                    <Image
                      src="/logo.png"
                      alt="Urvashi Logo"
                      fill
                      className="object-contain drop-shadow-lg"
                      priority
                      unoptimized
                    />
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
